import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProjectEntity } from '../entities/project.entity';
import { BaseRepository } from '../repositories/base.repository';
import { GetDeliverablesCompactResponse } from 'src/dtos/responses/get-deliverables-compact.dto';

@Injectable()
export class ProjectRepository extends BaseRepository<ProjectEntity> {
  constructor(
    @InjectRepository(ProjectEntity)
    public repository: Repository<ProjectEntity>,
  ) {
    super(repository);
  }

  async findByUid(uid: string): Promise<ProjectEntity | null> {
    return this.repository.findOne({
      where: { uid },
      relations: ['deliverables', 'deliverables.deliverable_type'],
    });
  }

  async findCompactWithFilters(
    search?: string,
    year?: number,
    functions?: string[],
    is_active?: boolean,
  ): Promise<GetDeliverablesCompactResponse> {
    console.log('Searching projects with filters:');
    const query = this.repository.createQueryBuilder('project');

    if (is_active !== undefined) {
      query.where('project.is_active = :is_active', {
        is_active: is_active ? 1 : 0,
      });
    } else {
      query.where('project.is_active = 1');
    }

    if (search) {
      query.andWhere('project.name LIKE :search', { search: `%${search}%` });
    }

    if (year) {
      query.andWhere('project.date_start BETWEEN :start AND :end', {
        start: `${year}-01-01`,
        end: `${year}-12-31`,
      });
    }

    if (functions && functions.length > 0) {
      query.andWhere('project.function IN (:...functions)', {
        functions,
      });
    }

    query.orderBy('project.name', 'ASC');

    const entities = await query
      .select([
        'project.uid',
        'project.name',
        'project.function',
        'project.is_active',
      ])
      .getMany();

    return {
      data: entities.map((project) => ({
        uid: project.uid,
        name: project.name,
        function: project.function,
        type: project.project_type,
        is_active: project.is_active,
        usage: Math.ceil(Math.random() * 100),
      })),
      pageNumber: 1,
      pageSize: entities.length,
      totalRecords: entities.length,
    };
  }
}
