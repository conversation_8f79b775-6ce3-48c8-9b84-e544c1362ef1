#!/bin/bash

# Set the correct Node.js version
echo "Setting Node.js version to 22.16.0..."
source ~/.nvm/nvm.sh
nvm use 22.16.0

# Set environment variables
export DATABASE_HOST=peopleproductssql.database.windows.net
export DATABASE_PORT=1433
export DATABASE_USERNAME=dantonheuer
export DATABASE_PASSWORD=Colorado@123
export DATABASE_NAME=tscdatabase
export DATABASE_SCHEMA=dbo
export PORT=2135
export CURRENT_SCHEMA=dbo
export REDIS_HOST=localhost
export REDIS_PORT=6379
export SESSION_SECRET='R4nd0n$ecr3tM0uT$'
export SESSION_NAME=str_token
export SESSION_SECURE=false
export CSURF_SECRET='%0$OxVNtLl%$pDGJmFTWEGqzYfmOBBql'
export API_KEY_AUTH='e2Yt^1*Aq6*i^7itVf8l'
export APP_ENV=local
export DD_ENV=DEVEL
export ENABLE_DB=false
export DD_TRACE_ENABLED=false
export NODE_ENV=development

echo "Starting NestJS in debug mode..."
echo "Node version: $(node --version)"
echo "Debug port: 9229"
echo ""
echo "You can now:"
echo "1. Set breakpoints in VSCode"
echo "2. Use F5 to start debugging, or"
echo "3. Use 'Attach to Process' configuration"
echo ""

# Start the application in debug mode
yarn start:debug
