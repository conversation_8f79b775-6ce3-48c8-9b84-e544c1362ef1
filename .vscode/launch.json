{"version": "0.2.0", "configurations": [{"name": "Debug NestJS App", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@nestjs/cli/bin/nest.js", "args": ["start", "--debug", "--watch"], "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "restart": true, "protocol": "inspector", "stopOnEntry": false, "runtimeArgs": ["--<PERSON><PERSON><PERSON>"], "sourceMaps": true, "cwd": "${workspaceFolder}", "outputCapture": "std"}, {"name": "Debug NestJS App (No Watch)", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@nestjs/cli/bin/nest.js", "args": ["start", "--debug"], "env": {"NODE_ENV": "development"}, "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "restart": false, "protocol": "inspector", "stopOnEntry": false, "runtimeArgs": ["--<PERSON><PERSON><PERSON>"], "sourceMaps": true, "cwd": "${workspaceFolder}", "outputCapture": "std"}, {"name": "Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--watchAll=false"], "env": {"NODE_ENV": "test"}, "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}, "runtimeArgs": ["--inspect-brk"], "sourceMaps": true, "cwd": "${workspaceFolder}", "outputCapture": "std"}, {"name": "Attach to Process", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "sourceMaps": true}]}