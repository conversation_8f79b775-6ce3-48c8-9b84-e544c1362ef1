{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true}, "search.exclude": {"**/node_modules": true, "**/dist": true}, "debug.node.autoAttach": "on", "debug.terminal.clearBeforeReusing": true}