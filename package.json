{"name": "@ghq-abi/tsc-kpi-catalog-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engine": {"node": ">=22.16.0", "npm": ">=10.9.2", "yarn": ">=1.22.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "dev": "nest start --watch", "debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js"}, "dependencies": {"@nestjs/common": "^11.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^11.1.5", "@nestjs/swagger": "^8.0.7", "@nestjs/typeorm": "^10.0.2", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.7", "@types/express-session": "^1.18.0", "@types/helmet": "^4.0.0", "@types/lodash": "^4.17.16", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.1", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "dd-trace": "^5.27.0", "express-session": "^1.18.2", "helmet": "^8.0.0", "lodash": "^4.17.21", "mssql": "^11.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.24", "typeorm-naming-strategies": "^4.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "resolutions": {"brace-expansion": "2.0.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}